<template>
  <div class="common-list">
    <!-- 搜索组件 -->
    <common-search
      :fields="searchFields"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 表格组件 -->
    <common-table
      :columns="columns"
      :data="data"
      :actions="actions"
      :page="page"
      @selection-change="handleSelectionChange"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      @add="handleAdd"
      @view="handleView"
      @edit="handleEdit"
      @delete="handleDelete"
      @batch-delete="handleBatchDelete"
      @info="handleInfo"
    />

    <!-- 抽屉表单 -->
    <common-drawer
      :visible.sync="drawerVisible"
      :title="drawerTitle"
      :mode="drawerMode"
      :fields="formFields"
      :form-data="formData"
      :rules="formRules"
      @submit="handleDrawerSubmit"
      @close="handleDrawerClose">
      <!-- 如果有自定义的表单项,可以通过插槽传入 -->
      <template v-for="field in formFields" v-slot:[field.prop]="scope">
        <slot :name="field.prop" v-bind="scope"></slot>
      </template>
    </common-drawer>
  </div>
</template>

<script>
import CommonSearch from '../CommonSearch'
import CommonTable from '../CommonTable'
import CommonDrawer from '../CommonDrawer'

export default {
  name: 'CommonList',
  components: {
    CommonSearch,
    CommonTable,
    CommonDrawer
  },
  props: {
    columns: {
      type: Array,
      required: true
    },
    searchFields: {
      type: Array,
      default: () => []
    },
    formFields: {
      type: Array,
      required: true
    },
    formRules: {
      type: Object,
      default: () => ({})
    },
    actions: {
      type: Array,
      default: () => []
    },
    fetchData: {
      type: Function,
      required: true
    },
    getDetail: {
      type: Function,
      default: null
    },
    paginationConfig: {
      type: Object,
      default: () => ({
        pageSizes: [10, 20, 30, 50],
        layout: 'total, sizes, prev, pager, next, jumper',
        pageSize: 20,
        currentPage: 1,
        total: 0
      })
    }
  },
  data() {
    return {
      data: [],
      selectedRows: [],
      page: {
        current: this.paginationConfig.currentPage,
        size: this.paginationConfig.pageSize,
        total: 0
      },
      searchParams: {},
      drawerVisible: false,
      drawerTitle: '',
      drawerMode: 'edit',
      formData: {}
    }
  },
  created() {
    this.loadData()
  },
  methods: {
    async loadData() {
      try {
        const params = {
          ...this.searchParams,
          page: this.page.current,
          limit: this.page.size
        }
        const res = await this.fetchData(params)
        this.data = res.list
        this.page.total = res.total
        this.page.current = res.page
        this.page.size = res.limit
      } catch (error) {
        console.error(error)
      }
    },
    handleSearch(params) {
      this.searchParams = params
      this.page.current = 1
      this.loadData()
    },
    handleReset() {
      this.searchParams = {}
      this.page.current = 1
      this.page.size = this.paginationConfig.pageSize
      this.loadData()
    },
    handleSelectionChange(val) {
      this.selectedRows = val
    },
    handleSizeChange(val) {
      this.page.size = val
      this.loadData()
    },
    handleCurrentChange(val) {
      this.page.current = val
      this.loadData()
    },
    handleAdd() {
      this.drawerVisible = true
      this.drawerTitle = '新增'
      this.drawerMode = 'add'
      this.formData = {}
    },
    async handleView(row) {
      try {
        let detailData = row
        if (this.getDetail) {
          const res = await this.getDetail(row)
          if (res.status === 200) {
            detailData = res.data
          } else {
            this.$message.error(res.message || '获取详情失败')
            return
          }
        }
        this.drawerTitle = '查看'
        this.drawerMode = 'view'
        this.formData = { ...detailData }
        this.drawerVisible = true
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$message.error('获取详情失败')
      }
    },
    async handleEdit(row) {
      try {
        let detailData = row
        if (this.getDetail) {
          const res = await this.getDetail(row)
          if (res.status === 200) {
            detailData = res.data
          } else {
            this.$message.error(res.message || '获取详情失败')
            return
          }
        }
        this.drawerTitle = '编辑'
        this.drawerMode = 'edit'
        this.formData = { ...detailData }
        this.drawerVisible = true
      } catch (error) {
        console.error('获取详情失败:', error)
        this.$message.error('获取详情失败')
      }
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该记录?', '提示', {
          type: 'warning'
        })
        this.$emit('delete', row)
      } catch (error) {
        console.error(error)
      }
    },
    async handleBatchDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请选择要删除的数据')
        return
      }
      try {
        await this.$confirm(`确认删除选中的 ${this.selectedRows.length} 条记录?`, '提示', {
          type: 'warning'
        })
        this.$emit('batch-delete', this.selectedRows)
      } catch (error) {
        console.error(error)
      }
    },
    handleInfo(row) {
      this.$emit('info', row)
    },
    async handleDrawerSubmit(formData) {
      try {
        let success = false
        if (this.drawerMode === 'add') {
          success = await this.$emit('add', formData)
        } else if (this.drawerMode === 'edit') {
          success = await this.$emit('edit', formData)
        }
        
        if (success) {
          this.drawerVisible = false
          this.loadData()
          this.$message.success(this.drawerMode === 'add' ? '添加成功' : '更新成功')
        }
      } catch (error) {
        console.error('提交失败:', error)
        this.$message.error('提交失败')
      }
    },
    handleDrawerClose() {
      this.drawerVisible = false
      this.drawerTitle = ''
      this.drawerMode = 'edit'
      this.formData = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.common-list {
  // padding: 20px;
  margin: 0 15px 15px;
  height: calc(100% - 30px);
  display: flex;
  flex-direction: column;
  
  .title-line {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
    .vertical-line {
      width: 4px;
      height: 16px;
      background-color: #409EFF;
      margin-right: 8px;
    }
    
    .title {
      font-size: 16px;
      font-weight: bold;
    }
    
    .horizontal-line {
      flex: 1;
      height: 1px;
      background-color: #EBEEF5;
      margin-left: 8px;
    }
    
    .operation-btns {
      margin-left: 16px;
    }
  }
  
  .search-wrapper {
    background-color: #fff;
    padding: 20px;
    margin-bottom: 20px;
    flex-shrink: 0;
  }
  
  .table-wrapper {
    background-color: #fff;
    padding: 20px;
    flex: 1;
    overflow: auto;
  }
  
  .delete-btn {
    color: #F56C6C;
  }

  :deep(.el-drawer__wrapper) {
    .el-drawer {
      .el-drawer__body {
        padding: 0;
        
        .el-form {
          .el-form-item {
            .el-form-item__content {
              .el-select {
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
</style> 