<template>
  <div class="table-wrapper">
    <div class="title-line">
      <span class="vertical-line"></span>
      <span class="title">{{ title }}</span>
      <span class="horizontal-line"></span>
      <div class="operation-btns">
        <el-button size="small" type="primary" class="add-btn" @click="$emit('add')" v-if="actions.includes('add')">新增</el-button>
        <el-button size="small" class="delete-btn" @click="handleBatchDelete" v-if="actions.includes('batchDelete')">删除</el-button>
        <slot name="buttons"></slot>
      </div>
    </div>
    
    <el-table
      :data="data"
      @selection-change="handleSelectionChange"
      :max-height="maxHeight"
      border
      stripe>
      <el-table-column type="selection" width="55" align="center" v-if="actions.includes('batchDelete')"></el-table-column>
      <template v-for="col in columns">
        <!-- 普通文本列 -->
        <el-table-column 
          v-if="!col.type"
          :key="col.prop"
          :prop="col.prop"
          :label="col.label"
          :width="col.width"
          :align="col.align || 'left'">
          <template slot-scope="scope">
            <template v-if="col.formatter">
              {{ col.formatter(scope.row, col, scope.row[col.prop]) }}
            </template>
            <template v-else>
              {{ scope.row[col.prop] }}
            </template>
          </template>
        </el-table-column>

        <!-- 标签类型列 -->
        <el-table-column 
          v-else-if="col.type === 'tag'"
          :key="col.prop"
          :label="col.label"
          :width="col.width"
          :align="col.align || 'center'">
          <template slot-scope="scope">
            <el-tag 
              :type="getTagType(scope.row[col.prop], col.tagMap)"
              size="mini">
              {{ getTagText(scope.row[col.prop], col.tagMap) }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 多标签类型列 -->
        <el-table-column 
          v-else-if="col.type === 'tags'"
          :key="col.prop"
          :label="col.label"
          :width="col.width"
          :align="col.align || 'center'">
          <template slot-scope="scope">
            <el-tag 
              v-for="(tag, index) in scope.row[col.prop]"
              :key="index"
              :type="col.tagType || ''"
              size="mini"
              class="mr-5">
              {{ tag }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 链接类型列 -->
        <el-table-column 
          v-else-if="col.type === 'link'"
          :key="col.prop"
          :label="col.label"
          :width="col.width"
          :align="col.align || 'left'">
          <template slot-scope="scope">
            <el-link 
              type="primary" 
              @click="handleColumnClick(col.prop, scope.row)">
              {{ scope.row[col.prop] }}
            </el-link>
          </template>
        </el-table-column>

        <!-- 自定义插槽列 -->
        <el-table-column 
          v-else-if="col.type === 'slot'"
          :key="col.prop"
          :label="col.label"
          :width="col.width"
          :align="col.align || 'left'">
          <template slot-scope="scope">
            <template v-if="col.formatter">
              {{ col.formatter(scope.row[col.prop]) }}
            </template>
            <template v-else>
              {{ scope.row[col.prop] }}
            </template>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column 
          v-else-if="col.type === 'action'"
          :key="col.prop || 'action'"
          :label="col.label || '操作'"
          :width="col.width || '240'"
          :align="col.align || 'center'"
          :fixed="col.fixed || 'right'">
          <template slot-scope="scope">
            <div class="action-buttons">
              <el-button 
                v-for="action in col.actions || rowActions"
                :key="action.type"
                type="text"
                size="mini"
                :class="[
                  action.type === 'delete' ? 'delete-btn' : '',
                  action.type === 'view' ? 'view-btn' : '',
                  action.type === 'edit' ? 'edit-btn' : '',
                  action.class
                ]"
                @click="handleAction(action.type, scope.row)">
                {{ action.text }}
              </el-button>
            </div>
          </template>
        </el-table-column>
      </template>

      <!-- 默认操作列 -->
      <el-table-column 
        label="操作" 
        width="240" 
        align="center" 
        v-if="actions.length && !hasActionColumn"
        fixed="right">
        <template slot-scope="scope">
          <div class="action-buttons">
            <el-button 
              v-for="action in rowActions"
              :key="action.type"
              type="text"
              size="mini"
              :class="[
                action.type === 'delete' ? 'delete-btn' : '',
                action.type === 'view' ? 'view-btn' : '',
                action.type === 'edit' ? 'edit-btn' : '',
                action.class
              ]"
              @click="handleAction(action.type, scope.row)">
              {{ action.text }}
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="page.current"
        :page-sizes="[10, 20, 30, 50]"
        :page-size="page.size"
        layout="total, sizes, prev, pager, next, jumper"
        :total="page.total"
        background>
      </el-pagination>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommonTable',
  props: {
    title: {
      type: String,
      default: '机构列表'
    },
    columns: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      default: () => []
    },
    actions: {
      type: Array,
      default: () => []
    },
    rowActions: {
      type: Array,
      default: () => [
        { type: 'view', text: '查看' },
        { type: 'edit', text: '编辑' },
        { type: 'delete', text: '删除', class: 'delete-btn' },
        { type: 'info', text: '详情' }
      ]
    },
    page: {
      type: Object,
      default: () => ({
        current: 1,
        size: 100,
        total: 0
      })
    },
    maxHeight: {
      type: Number,
      default: 450
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.$emit('selection-change', val)
    },
    handleAction(type, row) {
      this.$emit(type, row)
    },
    handleColumnClick(prop, row) {
      this.$emit('column-click', { prop, row })
    },
    getTagType(value, tagMap) {
      if (!tagMap) return ''
      return value === tagMap.true ? 'success' : 'danger'
    },
    getTagText(value, tagMap) {
      if (!tagMap) return value
      return value === tagMap.true ? tagMap.trueLabel : tagMap.falseLabel
    },
    handleSizeChange(val) {
      this.$emit('size-change', val)
    },
    handleCurrentChange(val) {
      this.$emit('current-change', val)
    },
    handleBatchDelete() {
      this.$emit('batch-delete')
    }
  },
  computed: {
    hasActionColumn() {
      return this.columns.some(col => col.type === 'action')
    }
  }
}
</script>

<style lang="scss" scoped>
.table-wrapper {
  background-color: #fff;
  padding: 16px 20px;
  border-radius: 4px;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .title-line {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
    flex-shrink: 0;
    
    .vertical-line {
      width: 4px;
      height: 16px;
      background-color: #409EFF;
      border-radius: 2px;
    }
    
    .title {
      position: relative;
      left: 18px;
      height: 23px;
      font-family: Source Han Sans;
      font-size: 16px;
      font-weight: 500;
      line-height: normal;
      letter-spacing: 0.89px;
      color: #000000;
    }
    
    .horizontal-line {
      flex: 1;
      height: 1px;
      background-color: #EBEEF5;
      margin-left: 30px;
    }
    
    .operation-btns {
      margin-left: 12px;
    }
  }

  :deep(.el-table) {
    margin: 15px 0;
    flex: 1;
    
    th {
      background-color: #F5F7FA;
      color: #606266;
      font-weight: 500;
      padding: 8px 0;
    }
    
    td {
      padding: 8px 0;
    }

    .action-buttons {
      display: flex;
      justify-content: center;
      flex-wrap: wrap;
    }

    .el-button--text {
      padding: 4px 8px;
      font-size: 12px;
      margin: 0 4px;
      height: auto;
      
      &.delete-btn {
        color: #F56C6C;
        background: #FEF0F0;
        border: 1px solid #F56C6C;
        border-radius: 4px;
        font-family: Inter;
        font-weight: 500;
      }
      
      &.view-btn {
        color: #67C23A;
        background: #F0F9EB;
        border: 1px solid #C2E7B0;
        border-radius: 4px;
        font-family: Inter;
        font-weight: 500;
        letter-spacing: 0px;
      }
      
      &.edit-btn {
        color: #409EFF;
        background: #ECF5FF;
        border: 1px solid #9FCEFF;
        border-radius: 4px;
        font-family: Inter;
        font-weight: 500;
        letter-spacing: 0px;
      }
    }
  }
  
  .pagination-container {
    margin-top: 16px;
    padding-top: 16px;
    text-align: center;
    flex-shrink: 0;

    :deep(.el-pagination) {
      font-size: 13px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      
      .btn-prev, .btn-next {
        padding: 0;
        min-width: 24px;
        height: 24px;
      }

      .el-pager li {
        min-width: 24px;
        height: 24px;
        line-height: 24px;
        font-size: 13px;
        margin: 0 4px;
      }

      .el-pagination__total {
        margin-right: 12px;
      }

      .el-pagination__jump {
        margin-left: 12px;
      }
    }
  }

  .mr-5 {
    margin-right: 5px;
  }

  .add-btn {
    color: #409EFF;
    background: #ECF5FF;
    border: 1px solid #9FCEFF;
    border-radius: 4px;
    font-family: Inter;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0px;
  }

  .delete-btn {
    background: #FEF0F0;
    border: 1px solid #F56C6C;
    color: #F56C6C;
    border-radius: 4px;
    font-family: Inter;
    font-size: 12px;
    font-weight: 500;
    letter-spacing: 0px;

    &:hover {
      background: #FEF0F0;
      border-color: #F56C6C;
      color: #F56C6C;
    }
  }
}
</style> 