import Vue from 'vue'
import Router from 'vue-router'
import settings from "@root/publicMethods/settings";

Vue.use(Router)

export function createRouter() {
  return new Router({
    mode: 'history',
    base: process.env.BASE_URL,
    scrollBehavior: () => ({
      y: 0
    }),
    routes: [
      {
        path: settings.admin_base_path + '/orgManage',
        name: 'OrgManage',
        component: () => import('@/views/orgManage/index.vue'),
        meta: {
          title: '机构管理',
          icon: 'organization'
        }
      },
      {
        path: settings.admin_base_path + '/orgManage/info',
        name: 'OrgInfo',
        component: () => import('@/views/orgManage/info.vue'),
        meta: {
          title: '机构详情',
          icon: 'organization'
        }
      }
    ]
  })
}

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router