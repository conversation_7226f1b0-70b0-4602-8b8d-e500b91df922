<template>
  <div class="app-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <el-button @click="handleBack" icon="el-icon-arrow-left" type="primary" plain size="small">
        返回列表
      </el-button>
      <h2 class="page-title">机构详情</h2>
    </div>

    <div v-loading="loading" class="detail-content">
      <!-- 机构基本信息 -->
      <div class="info-section">
        <div class="section-header">
          <h3 class="section-title">
            <i class="el-icon-office-building"></i>
            机构基本信息
          </h3>
        </div>

        <div class="info-content">
          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">机构名称</label>
                <div class="info-value">{{ institutionData.name || '-' }}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">所在地区</label>
                <div class="info-value">{{ institutionData.regionName || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-col full-width">
              <div class="info-item">
                <label class="info-label">单位类型</label>
                <div class="info-value">
                  <el-tag
                    v-for="type in institutionData.orgTypes"
                    :key="type"
                    size="small"
                    type="primary"
                    class="type-tag"
                  >
                    {{ getInstitutionTypeLabel(type) }}
                  </el-tag>
                  <span v-if="!institutionData.orgTypes || institutionData.orgTypes.length === 0">-</span>
                </div>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">统一社会信用代码</label>
                <div class="info-value">{{ institutionData.creditCode || '-' }}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">法定代表人</label>
                <div class="info-value">{{ institutionData.legalPerson || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-col full-width">
              <div class="info-item">
                <label class="info-label">注册地址</label>
                <div class="info-value">{{ institutionData.address || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">联系人</label>
                <div class="info-value">{{ institutionData.contactPerson || '-' }}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">联系电话</label>
                <div class="info-value">{{ institutionData.contactPhone || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">机构编码</label>
                <div class="info-value">{{ institutionData.orgCode || '-' }}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">行业代码</label>
                <div class="info-value">{{ institutionData.industryName || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">机构级别</label>
                <div class="info-value">{{ institutionData.orgLevelName || '-' }}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">邮箱</label>
                <div class="info-value">{{ institutionData.email || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">经度</label>
                <div class="info-value">{{ institutionData.latitude || '-' }}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">纬度</label>
                <div class="info-value">{{ institutionData.longitude || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="info-row">
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">邮编</label>
                <div class="info-value">{{ institutionData.postalCode || '-' }}</div>
              </div>
            </div>
            <div class="info-col">
              <div class="info-item">
                <label class="info-label">传真号码</label>
                <div class="info-value">{{ institutionData.fax || '-' }}</div>
              </div>
            </div>
          </div>

          <div class="info-row" v-if="institutionData.businessLicense">
            <div class="info-col full-width">
              <div class="info-item">
                <label class="info-label">营业执照</label>
                <div class="info-value">
                  <a v-if="institutionData.businessLicense.fileUrl"
                     :href="institutionData.businessLicense.fileUrl"
                     target="_blank"
                     class="file-link">
                    {{ institutionData.businessLicense.fileName || '查看营业执照' }}
                  </a>
                  <span v-else>{{ institutionData.businessLicense.fileName || '-' }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="info-row" v-if="institutionData.remark">
            <div class="info-col full-width">
              <div class="info-item">
                <label class="info-label">备注</label>
                <div class="info-value">{{ institutionData.remark }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 机构信息 -->
      <div class="info-section" v-if="hasAnyInstitutionInfo()">
        <div class="section-header">
          <h3 class="section-title">
            <i class="el-icon-document"></i>
            第三方机构类型详情
          </h3>
          <span class="section-subtitle">按机构类别显示</span>
        </div>

        <!-- 动态显示允许的机构类型信息 -->
        <div v-for="orgType in filteredOrgTypes" :key="orgType" class="institution-type-section">
          <el-collapse v-model="activeCollapse" class="institution-collapse">
            <el-collapse-item :name="orgType" class="collapse-item">
              <template slot="title">
                <div class="collapse-title">
                  <i :class="getInstitutionTypeIcon(orgType)"></i>
                  <span>{{ getInstitutionTypeLabel(orgType) }}</span>
                </div>
              </template>
              <div class="collapse-content">
                <!-- 医疗机构信息 (210) -->
                <div v-if="orgType === '210' && institutionData.extensions && institutionData.extensions['210']">
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">医疗许可证号</label>
                        <div class="info-value">{{ institutionData.extensions['210'].medicalLicense || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">发证机关</label>
                        <div class="info-value">{{ institutionData.extensions['210'].licenseAuthority || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">发证日期</label>
                        <div class="info-value">{{ formatDate(institutionData.extensions['210'].licenseDate) }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">医疗许可证文件</label>
                        <div class="info-value">
                          <a v-if="institutionData.extensions['210'].medicalLicenseFile && institutionData.extensions['210'].medicalLicenseFile.fileUrl"
                             :href="institutionData.extensions['210'].medicalLicenseFile.fileUrl"
                             target="_blank"
                             class="file-link">
                            {{ institutionData.extensions['210'].medicalLicenseFile.fileName || '查看医疗许可证' }}
                          </a>
                          <span v-else>-</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['210'].medicalSubTypes && institutionData.extensions['210'].medicalSubTypes.length > 0">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">医疗子类型</label>
                        <div class="info-value">
                          <el-tag
                            v-for="subType in institutionData.extensions['210'].medicalSubTypes"
                            :key="subType"
                            size="small"
                            type="info"
                            class="type-tag"
                          >
                            {{ subType }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">机构类型</label>
                        <div class="info-value">{{ institutionData.extensions['210'].institutionTypeName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">管理类型</label>
                        <div class="info-value">{{ institutionData.extensions['210'].managementTypeName || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">机构分等</label>
                        <div class="info-value">{{ institutionData.extensions['210'].orgClassName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">机构分级</label>
                        <div class="info-value">{{ institutionData.extensions['210'].orgGradeName || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['210'].radiationDiagnosisLicense">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">放射诊疗许可证编号</label>
                        <div class="info-value">{{ institutionData.extensions['210'].radiationDiagnosisLicense || '-' }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 职业健康检查机构信息 (211) -->
                <div v-else-if="orgType === '211' && institutionData.extensions && institutionData.extensions['211']">
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">备案编号</label>
                        <div class="info-value">{{ institutionData.extensions['211'].recordNumber || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">备案日期</label>
                        <div class="info-value">{{ formatDate(institutionData.extensions['211'].recordDate) }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">备案单位名称</label>
                        <div class="info-value">{{ institutionData.extensions['211'].recordOrgName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">咨询电话</label>
                        <div class="info-value">{{ institutionData.extensions['211'].consultPhone || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">是否具备外出体检能力</label>
                        <div class="info-value">
                          <el-tag :type="institutionData.extensions['211'].hasOutdoorCheckCapability ? 'success' : 'info'" size="small">
                            {{ institutionData.extensions['211'].hasOutdoorCheckCapability ? '是' : '否' }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['211'].checkCategories && institutionData.extensions['211'].checkCategories.length > 0">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">可开展的职业健康检查类别及项目</label>
                        <div class="info-value">
                          <el-tag
                            v-for="category in institutionData.extensions['211'].checkCategories"
                            :key="category"
                            size="small"
                            type="success"
                            class="type-tag"
                          >
                            {{ getHealthCheckCategoryLabel(category) }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['211'].introduction">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">机构简介</label>
                        <div class="info-value text-content">{{ institutionData.extensions['211'].introduction }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 职业病诊断机构信息 (212) -->
                <div v-else-if="orgType === '212' && institutionData.extensions && institutionData.extensions['212']">
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">备案编号</label>
                        <div class="info-value">{{ institutionData.extensions['212'].recordNumber || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">备案日期</label>
                        <div class="info-value">{{ formatDate(institutionData.extensions['212'].recordDate) }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">备案单位名称</label>
                        <div class="info-value">{{ institutionData.extensions['212'].recordOrgName || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['212'].diagnosisCategories && institutionData.extensions['212'].diagnosisCategories.length > 0">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">职业病诊断类别及病种</label>
                        <div class="info-value">
                          <el-tag
                            v-for="category in institutionData.extensions['212'].diagnosisCategories"
                            :key="category"
                            size="small"
                            type="warning"
                            class="type-tag"
                          >
                            {{ getDiagnosisCategoryLabel(category) }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['212'].diagnosisAreasNames && institutionData.extensions['212'].diagnosisAreasNames.length > 0">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">可以开展诊断的辖区</label>
                        <div class="info-value">
                          <el-tag
                            v-for="area in institutionData.extensions['212'].diagnosisAreasNames"
                            :key="area"
                            size="small"
                            type="primary"
                            class="type-tag"
                          >
                            {{ area }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 职业病鉴定机构信息 (220) -->
                <div v-else-if="orgType === '220' && institutionData.extensions && institutionData.extensions['220']">
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">鉴定机构类别</label>
                        <div class="info-value">{{ institutionData.extensions['220'].identificationLevelName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">鉴定费</label>
                        <div class="info-value">{{ institutionData.extensions['220'].identificationFee ? `${institutionData.extensions['220'].identificationFee}元` : '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">收款人全称</label>
                        <div class="info-value">{{ institutionData.extensions['220'].payeeName || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">账号</label>
                        <div class="info-value">{{ institutionData.extensions['220'].payeeAccount || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">开户行</label>
                        <div class="info-value">{{ institutionData.extensions['220'].payeeBank || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['220'].workingHours">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">工作时间</label>
                        <div class="info-value">
                          {{ institutionData.extensions['220'].workingHours.start || '-' }} - {{ institutionData.extensions['220'].workingHours.end || '-' }}
                        </div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">工作日</label>
                        <div class="info-value">
                          <span v-if="institutionData.extensions['220'].workingHours.workDays && institutionData.extensions['220'].workingHours.workDays.length > 0">
                            {{ institutionData.extensions['220'].workingHours.workDays.join('、') }}
                          </span>
                          <span v-else>-</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 职业卫生技术服务机构信息 (230) -->
                <div v-else-if="orgType === '230' && institutionData.extensions && institutionData.extensions['230']">
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">服务资质证书编号</label>
                        <div class="info-value">{{ institutionData.extensions['230'].serviceQualification || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">发证机关</label>
                        <div class="info-value">{{ institutionData.extensions['230'].licenseAuthority || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">发证日期</label>
                        <div class="info-value">{{ formatDate(institutionData.extensions['230'].licenseDate) }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">有效期至</label>
                        <div class="info-value">{{ formatDate(institutionData.extensions['230'].validDate) }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['230'].technicalServiceCoverageNames && institutionData.extensions['230'].technicalServiceCoverageNames.length > 0">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">技术服务范围</label>
                        <div class="info-value">
                          <el-tag
                            v-for="coverage in institutionData.extensions['230'].technicalServiceCoverageNames"
                            :key="coverage"
                            size="small"
                            type="info"
                            class="type-tag"
                          >
                            {{ coverage }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 职业病康复机构信息 (213) -->
                <div v-else-if="orgType === '213' && institutionData.extensions && institutionData.extensions['213']">
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">机构类别</label>
                        <div class="info-value">{{ institutionData.extensions['213'].rehabilitationTypeName || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['213'].serviceAreasNames && institutionData.extensions['213'].serviceAreasNames.length > 0">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">服务辖区</label>
                        <div class="info-value">
                          <el-tag
                            v-for="area in institutionData.extensions['213'].serviceAreasNames"
                            :key="area"
                            size="small"
                            type="success"
                            class="type-tag"
                          >
                            {{ area }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 放射卫生技术服务机构信息 (240) -->
                <div v-else-if="orgType === '240' && institutionData.extensions && institutionData.extensions['240']">
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">资质证书编号</label>
                        <div class="info-value">{{ institutionData.extensions['240'].radiationQualification || '-' }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">发证机关</label>
                        <div class="info-value">{{ institutionData.extensions['240'].licenseAuthority || '-' }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row">
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">发证日期</label>
                        <div class="info-value">{{ formatDate(institutionData.extensions['240'].licenseDate) }}</div>
                      </div>
                    </div>
                    <div class="info-col">
                      <div class="info-item">
                        <label class="info-label">有效期至</label>
                        <div class="info-value">{{ formatDate(institutionData.extensions['240'].validDate) }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['240'].evaluationQualificationName">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">放射诊疗建设项目评价资质</label>
                        <div class="info-value">{{ institutionData.extensions['240'].evaluationQualificationName }}</div>
                      </div>
                    </div>
                  </div>
                  <div class="info-row" v-if="institutionData.extensions['240'].testingQualificationNames && institutionData.extensions['240'].testingQualificationNames.length > 0">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">检测资质</label>
                        <div class="info-value">
                          <el-tag
                            v-for="qualification in institutionData.extensions['240'].testingQualificationNames"
                            :key="qualification"
                            size="small"
                            type="warning"
                            class="type-tag"
                          >
                            {{ qualification }}
                          </el-tag>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 其他机构类型的通用显示 -->
                <div v-else-if="institutionData.extensions && institutionData.extensions[orgType]">
                  <div class="info-row">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <label class="info-label">扩展信息</label>
                        <div class="info-value">
                          <pre class="extension-info">{{ JSON.stringify(institutionData.extensions[orgType], null, 2) }}</pre>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 如果没有扩展信息 -->
                <div v-else>
                  <div class="info-row">
                    <div class="info-col full-width">
                      <div class="info-item">
                        <div class="info-value no-data">暂无该机构类型的详细信息</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>






      </div>
    </div>
  </div>
</template>

<script>
import { getInstitutionDetail, listByKey } from '@/api/techVendorManagement';

export default {
  name: 'InstitutionDetail',

  data() {
    return {
      loading: false,
      institutionData: {},
      activeCollapse: [], // 控制折叠面板的展开状态
      institutionTypeOptions: [],
      healthCheckCategoryOptions: [], // 职业健康检查类别选项
      diagnosisCategoryOptions: [], // 职业病诊断类别选项
      // 允许显示的机构类型
      allowedInstitutionTypes: ['210', '211', '212', '213', '220', '230', '240'],
      allOptions: [],
    };
  },

  computed: {
    // 过滤后的机构类型，只显示允许的类型
    filteredOrgTypes() {
      if (!this.institutionData.orgTypes) return [];
      return this.institutionData.orgTypes.filter(type =>
        this.allowedInstitutionTypes.includes(type)
      );
    }
  },

  methods: {
    // 返回列表
    handleBack() {
      this.$router.push({ name: 'InstitutionList' });
    },

    // 获取机构类型标签
    getInstitutionTypeLabel(type) {
      const option = this.allOptions.find(item => item.value === type);
      return option ? option.label : type;
    },

    // 获取机构类型选项
    async fetchInstitutionTypes() {
      try {
        // 调用接口获取机构类型字典数据
        const response = await listByKey({key: 'org_type'});

        if (response && response.data && Array.isArray(response.data)) {
          // 将接口返回的数据转换为选项格式
          const allOptions = response.data.map(item => ({
            value: item.value || item.code || item.key,
            label: item.label || item.name || item.text
          }));
          this.allOptions = allOptions;

          // 过滤只保留指定的机构类型
          this.institutionTypeOptions = allOptions.filter(option =>
            this.allowedInstitutionTypes.includes(option.value)
          );
        } else {
          console.log('机构类型数据接口返回格式错误，使用默认数据');
        }
      } catch (error) {
        console.error('获取机构类型数据失败:', error);
      }
    },

    // 检查是否包含某种机构类型
    hasInstitutionType(type) {
      return this.institutionData.orgTypes && this.institutionData.orgTypes.includes(type);
    },

    // 检查是否有任何机构信息需要显示
    hasAnyInstitutionInfo() {
      return this.filteredOrgTypes && this.filteredOrgTypes.length > 0;
    },

    // 获取鉴定机构类别标签
    getIdentificationCategoryLabel(category) {
      const labels = {
        'division_city': '师市级',
        'corps': '兵团级',
      };
      return labels[category] || '-';
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '-';
      try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN');
      } catch (error) {
        return '-';
      }
    },

    // 获取机构类型图标
    getInstitutionTypeIcon(orgType) {
      const iconMap = {
        '210': 'el-icon-s-home',        // 医疗机构
        '211': 'el-icon-s-check',       // 职业健康检查机构
        '212': 'el-icon-s-order',       // 职业病诊断机构
        '213': 'el-icon-s-help',        // 职业病康复机构
        '220': 'el-icon-s-claim',       // 职业病鉴定机构
        '230': 'el-icon-s-tools',       // 职业卫生技术服务机构
        '240': 'el-icon-warning',       // 放射卫生技术服务机构
        '215': 'el-icon-search',        // 其他机构类型
      };
      return iconMap[orgType] || 'el-icon-document';
    },

    // 获取职业健康检查类别标签
    getHealthCheckCategoryLabel(categoryValue) {
      const option = this.healthCheckCategoryOptions.find(item => item.value === categoryValue);
      return option ? option.label : categoryValue;
    },

    // 获取职业健康检查类别选项
    async fetchHealthCheckCategories() {
      try {
        // 调用接口获取职业健康检查类别字典数据
        const response = await listByKey({key: 'occupational_health_categories'});

        if (response && response.data && Array.isArray(response.data)) {
          // 将接口返回的数据转换为选项格式
          this.healthCheckCategoryOptions = response.data.map(item => ({
            value: item.value || item.code || item.key,
            label: item.label || item.name || item.text
          }));
        } else {
          console.log('职业健康检查类别数据接口返回格式错误，使用默认数据');
        }
      } catch (error) {
        console.error('获取职业健康检查类别数据失败:', error);
      }
    },

    // 获取职业病诊断类别标签
    getDiagnosisCategoryLabel(categoryValue) {
      const option = this.diagnosisCategoryOptions.find(item => item.value === categoryValue);
      return option ? option.label : categoryValue;
    },

    // 获取职业病诊断类别选项
    async fetchDiagnosisCategories() {
      try {
        // 调用接口获取职业病诊断类别字典数据
        const response = await listByKey({key: 'occupational_disease_categories'});

        if (response && response.data && Array.isArray(response.data)) {
          // 将接口返回的数据转换为选项格式
          this.diagnosisCategoryOptions = response.data.map(item => ({
            value: item.value || item.code || item.key,
            label: item.label || item.name || item.text
          }));
        } else {
          console.log('职业病诊断类别数据接口返回格式错误，使用默认数据');
        }
      } catch (error) {
        console.error('获取职业病诊断类别数据失败:', error);
      }
    },

    // 获取默认职业病诊断类别选项
    getDefaultDiagnosisCategories() {
      return [
        { value: '120001', label: '尘肺病' },
        { value: '120002', label: '职业性化学中毒' },
        { value: '120003', label: '职业性物理因素疾病' },
        { value: '120004', label: '职业性传染病' },
        { value: '120005', label: '职业性皮肤病' },
        { value: '120006', label: '职业性眼病' },
        { value: '120007', label: '职业性耳鼻喉口腔疾病' },
        { value: '120008', label: '职业性肿瘤' },
        { value: '120009', label: '其他职业病' },
      ];
    },

    // 获取机构详情
    async fetchInstitutionDetail() {
      const orgCode = this.$route.query.orgCode;
      console.log(orgCode, 22222222)
      if (!orgCode) {
        this.$message.error('机构编码不能为空');
        this.handleBack();
        return;
      }

      this.loading = true;

      try {
        // 调用接口获取机构详情
        const params = {
          idCode: orgCode // 根据接口文档，使用idCode参数
        };

        const response = await getInstitutionDetail(params);

        if (response && response.status === 200 && response.data) {
          // 解析接口返回的数据结构
          const { base, extensions } = response.data;

          // 转换数据格式以适配页面显示
          this.institutionData = {
            id: base._id,
            name: base.name,
            regionName: base.areaName,
            orgTypes: base.orgTypes || [],
            creditCode: base.creditCode,
            legalPerson: base.legalPerson,
            address: base.address,
            postalCode: base.postalCode,
            fax: base.fax,
            email: base.email,
            longitude: base.longitude,
            latitude: base.latitude,
            remark: base.remark,
            contactPerson: base.contactPerson,
            contactPhone: base.contactPhone,
            orgCode: base.orgCode,
            industryName: base.industryName,
            orgLevelName: base.orgLevelName,
            businessLicense: base.businessLicense,

            // 解析扩展信息 - 医疗机构 (210)
            medicalInfo: extensions && extensions['210'] ? {
              medicalLicense: extensions['210'].medicalLicense,
              licenseAuthority: extensions['210'].licenseAuthority,
              licenseDate: extensions['210'].licenseDate,
              medicalLicenseFile: extensions['210'].medicalLicenseFile,
              medicalSubTypes: extensions['210'].medicalSubTypes || []
            } : null,

            // 解析扩展信息 - 职业健康检查机构 (211)
            healthCheckInfo: extensions && extensions['211'] ? {
              recordNumber: extensions['211'].recordNumber,
              recordDate: extensions['211'].recordDate,
              hasOutsideCapability: extensions['211'].hasOutdoorCheckCapability,
              checkCategories: extensions['211'].checkCategories ? extensions['211'].checkCategories.join('、') : ''
            } : null,

            // 保存原始扩展信息，以便处理其他机构类型
            extensions: extensions || {}
          };
        } else {
          this.$message.error('获取机构详情失败：返回数据格式错误');
          this.institutionData = {};
        }

      } catch (error) {
        console.error('获取机构详情失败:', error);
        this.$message.error('获取机构详情失败，请稍后重试');

        // 如果接口调用失败，使用模拟数据作为备用
        console.log('使用模拟数据作为备用');
        this.institutionData = this.getMockInstitutionData();
      } finally {
        this.loading = false;
      }
    },

    // 模拟机构数据
    getMockInstitutionData() {
      const id = this.$route.params.id;
      const mockData = {
        '1': {
          id: '1',
          regionName: '新疆维吾尔自治区 乌鲁木齐市',
          name: '新疆维吾尔自治区职业病防治院',
          institutionTypes: ['occupational_health_check', 'occupational_disease_diagnosis', 'medical_institution'],
          creditCode: '91650000123456789X',
          legalPerson: '张建国',
          legalPersonIdCard: '650102198001011234',
          position: '院长/主任医师',
          address: '新疆维吾尔自治区乌鲁木齐市天山区解放南路123号',
          postalCode: '830000',
          fax: '0991-1234567',
          email: '<EMAIL>',
          longitude: 87.617733,
          latitude: 43.792818,
          remark: '自治区级职业病防治专业机构，承担全疆职业病防治技术指导工作',
          status: 'active',
          // 医疗机构信息
          medicalInfo: {
            managementType: 'non_profit',
            institutionType: 'public',
            unitLevel: 'corps',
            unitGrade: 'tertiary',
            unitRating: 'grade_a',
            licenseAuthority: '新疆维吾尔自治区卫生健康委员会',
            licenseNumber: 'PDY00001-X65010000001',
          },
          // 职业健康检查机构信息
          healthCheckInfo: {
            recordNumber: 'ZYJKJC-XJ-001',
            hasOutsideCapability: true,
            checkCategories: '接触粉尘作业人员职业健康检查、接触化学因素作业人员职业健康检查、接触物理因素作业人员职业健康检查、接触生物因素作业人员职业健康检查、接触放射性因素作业人员职业健康检查',
          },
          // 职业病诊断机构信息
          diagnosisInfo: {
            recordNumber: 'ZYBZD-XJ-001',
            diagnosisCategories: '尘肺病、职业性化学中毒、职业性物理因素疾病、职业性传染病、职业性皮肤病、职业性眼病、职业性耳鼻喉口腔疾病、职业性肿瘤、其他职业病',
          },
        },
        '2': {
          id: '2',
          regionName: '新疆维吾尔自治区 克拉玛依市',
          name: '克拉玛依市职业病防治中心',
          institutionTypes: ['occupational_health_check', 'hazard_factor_detection'],
          creditCode: '91650200987654321A',
          legalPerson: '李明华',
          legalPersonIdCard: '650202198502152345',
          position: '主任/副主任医师',
          address: '新疆维吾尔自治区克拉玛依市克拉玛依区友谊路456号',
          postalCode: '834000',
          fax: '0990-2345678',
          email: '<EMAIL>',
          longitude: 84.889207,
          latitude: 45.595886,
          remark: '服务克拉玛依地区石油化工企业职业健康',
          status: 'active',
          // 职业健康检查机构信息
          healthCheckInfo: {
            recordNumber: 'ZYJKJC-XJ-002',
            hasOutsideCapability: true,
            checkCategories: '接触粉尘作业人员职业健康检查、接触化学因素作业人员职业健康检查、接触物理因素作业人员职业健康检查',
          },
          // 危害因素检测机构信息
          hazardDetectionInfo: {
            recordNumber: 'WHYSJC-XJ-001',
            detectionScope: '工作场所空气中粉尘浓度检测、化学有害因素浓度检测、物理因素检测（噪声、振动、高温、低温等）',
          },
        },
        '3': {
          id: '3',
          regionName: '新疆维吾尔自治区 昌吉回族自治州',
          name: '昌吉州第一人民医院职业病科',
          institutionTypes: ['medical_institution', 'occupational_health_check'],
          creditCode: '91652300456789012B',
          legalPerson: '王志强',
          legalPersonIdCard: '652301197803203456',
          position: '院长/主任医师',
          address: '新疆维吾尔自治区昌吉市延安北路789号',
          postalCode: '831100',
          fax: '0994-3456789',
          email: '<EMAIL>',
          longitude: 87.304012,
          latitude: 44.014577,
          remark: '昌吉州综合性医院职业病科室',
          status: 'active',
          // 医疗机构信息
          medicalInfo: {
            managementType: 'non_profit',
            institutionType: 'public',
            unitLevel: 'division_city',
            unitGrade: 'tertiary',
            unitRating: 'grade_a',
            licenseAuthority: '昌吉回族自治州卫生健康委员会',
            licenseNumber: 'PDY00003-X65230000001',
          },
          // 职业健康检查机构信息
          healthCheckInfo: {
            recordNumber: 'ZYJKJC-XJ-003',
            hasOutsideCapability: false,
            checkCategories: '接触粉尘作业人员职业健康检查、接触化学因素作业人员职业健康检查',
          },
        },
        '5': {
          id: '5',
          regionName: '新疆维吾尔自治区 阿克苏地区',
          name: '阿克苏地区职业病鉴定委员会',
          institutionTypes: ['occupational_disease_identification'],
          creditCode: '91652900012345678D',
          legalPerson: '孙德华',
          legalPersonIdCard: '652901198012075678',
          position: '主任委员/主任医师',
          address: '新疆维吾尔自治区阿克苏市解放路654号',
          postalCode: '843000',
          fax: '0997-5678901',
          email: '<EMAIL>',
          longitude: 80.260605,
          latitude: 41.170712,
          remark: '阿克苏地区职业病鉴定权威机构',
          status: 'active',
          // 职业病鉴定机构信息
          identificationInfo: {
            category: 'division_city',
            diagnosisCategories: '尘肺病、职业性化学中毒、职业性物理因素疾病、职业性传染病、职业性皮肤病、职业性眼病、职业性耳鼻喉口腔疾病',
          },
        },
        '6': {
          id: '6',
          regionName: '新疆维吾尔自治区 喀什地区',
          name: '喀什地区第一人民医院',
          institutionTypes: ['medical_institution', 'occupational_disease_diagnosis', 'occupational_disease_rehabilitation'],
          creditCode: '91653100345678901E',
          legalPerson: '周建军',
          legalPersonIdCard: '653101197505186789',
          position: '院长/主任医师',
          address: '新疆维吾尔自治区喀什市人民西路987号',
          postalCode: '844000',
          fax: '0998-6789012',
          email: '<EMAIL>',
          longitude: 75.989138,
          latitude: 39.467664,
          remark: '喀什地区最大的综合性医院，设有职业病诊断和康复科室',
          status: 'active',
          // 医疗机构信息
          medicalInfo: {
            managementType: 'non_profit',
            institutionType: 'public',
            unitLevel: 'division_city',
            unitGrade: 'tertiary',
            unitRating: 'grade_a',
            licenseAuthority: '喀什地区卫生健康委员会',
            licenseNumber: 'PDY00006-X65310000001',
          },
          // 职业病诊断机构信息
          diagnosisInfo: {
            recordNumber: 'ZYBZD-XJ-002',
            diagnosisCategories: '尘肺病、职业性化学中毒、职业性物理因素疾病、职业性皮肤病',
          },
          // 职业病康复治疗机构信息
          rehabilitationInfo: {
            recordNumber: 'ZYBKF-XJ-001',
            treatmentProjects: '尘肺病康复治疗、职业性化学中毒康复治疗、职业性物理因素疾病康复治疗、职业性皮肤病康复治疗',
          },
        },
      };

      return mockData[id] || mockData['1']; // 如果找不到对应ID的数据，返回默认数据
    },
  },

  mounted() {
    this.fetchInstitutionTypes();
    this.fetchHealthCheckCategories();
    this.fetchDiagnosisCategories();
    this.fetchInstitutionDetail();
  },
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 24px;
  padding: 16px 0;

  .page-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #303133;
  }
}

.detail-content {
  .info-section {
    background: #fff;
    // border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;

    .section-header {
      padding: 20px 24px 16px;
      border-bottom: 1px solid #f0f2f5;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .section-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #409eff;
          font-size: 18px;
        }
      }

      .section-subtitle {
        font-size: 12px;
        color: #909399;
      }
    }

    .info-content {
      padding: 20px 24px;
    }

    .sub-section {
      margin-top: 24px;
      padding-top: 20px;
      border-top: 1px solid #f0f2f5;

      &:first-child {
        margin-top: 0;
        padding-top: 0;
        border-top: none;
      }

      .sub-title {
        margin: 0 0 16px 0;
        font-size: 14px;
        font-weight: 600;
        color: #606266;
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          color: #67c23a;
          font-size: 16px;
        }
      }
    }
  }

  .info-row {
    display: flex;
    margin-bottom: 16px;
    gap: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .info-col {
      flex: 1;

      &.full-width {
        flex: none;
        width: 100%;
      }
    }

    .info-item {
      .info-label {
        display: block;
        font-size: 13px;
        font-weight: 500;
        color: #606266;
        margin-bottom: 6px;
        line-height: 1.4;
      }

      .info-value {
        font-size: 14px;
        color: #303133;
        line-height: 1.5;
        word-break: break-all;

        &.text-content {
          line-height: 1.6;
          text-align: justify;
        }
      }
    }
  }

  .type-tag {
    margin-right: 8px;
    margin-bottom: 4px;
  }

  .file-link {
    color: #409eff;
    text-decoration: none;

    &:hover {
      color: #66b1ff;
      text-decoration: underline;
    }
  }

  .institution-collapse {
    margin-top: 16px;
    border: none;

    &:first-child {
      margin-top: 0;
    }

    .collapse-item {
      margin-bottom: 12px;
      border: 1px solid #e4e7ed;
      // border-radius: 6px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }

      :deep(.el-collapse-item__header) {
        background-color: #fafbfc;
        border-bottom: 1px solid #e4e7ed;
        padding: 12px 16px;
        font-weight: 500;
        color: #303133;

        &:hover {
          background-color: #f5f7fa;
        }

        .collapse-title {
          display: flex;
          align-items: center;
          gap: 8px;

          i {
            color: #409eff;
            font-size: 16px;
          }

          span {
            font-size: 14px;
          }
        }
      }

      :deep(.el-collapse-item__content) {
        padding: 0;
        border: none;

        .collapse-content {
          padding: 16px;
        }
      }

      :deep(.el-collapse-item__arrow) {
        color: #909399;
        font-weight: bold;
      }
    }
  }
}

@media (max-width: 768px) {
  .app-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .page-title {
      font-size: 18px;
    }
  }

  .detail-content {
    .info-section {
      .section-header {
        padding: 16px 20px 12px;
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .section-title {
          font-size: 15px;
        }
      }

      .info-content {
        padding: 16px 20px;
      }

      .sub-section {
        margin-top: 20px;
        padding-top: 16px;

        .sub-title {
          font-size: 13px;
          margin-bottom: 12px;
        }
      }
    }

    .info-row {
      flex-direction: column;
      gap: 12px;
      margin-bottom: 12px;

      .info-col {
        flex: none;
        width: 100%;
      }
    }
  }
}
</style>
