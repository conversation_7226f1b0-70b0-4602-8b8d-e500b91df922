import request from '@root/publicMethods/request';

/**
 * 根据字典类型key获取字典项列表
 * @param {string} key 字典类型标识
 */
export function getDictItemsByKey(params) {
  return request({
    url: '/manage/dictManage/listByKey',
    method: 'get',
    params
  });
}

// 获取字典子项
export function getDictChildren(params) {
  return request({
    url: '/manage/dictManage/getChildrenByParent',
    method: 'get',
    params
  });
} 

// 获取字典值链
export function getDictValueChain(params) {
  return request({
    url: '/manage/dictManage/getValueChain',
    method: 'get',
    params
  });
}

// 获取级联格式字典
export function getDictCascadeOptions(params) {
  return request({
    url: '/api/dictManage/getChainValue',
    method: 'get',
    params,
  });
}