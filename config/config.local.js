'use strict';
const path = require('path');
const isDocker = process.env.BUILD_ENV === 'docker';
const mongohost = isDocker ? 'mongodb' : '127.0.0.1:27017';
const mongobin = isDocker
  ? ''
  : '/Users/<USER>/Documents/dora/softs/mongodb/bin/';

module.exports = appInfo => {
  return {
    admin_root_path: 'http://localhost',
    // DEV_CONFIG_MODULES_BEGIN
    dev_modules: [
      // 'navbar',
      // 'dashboard',
      // 'adminGroup',
      // 'adminUser',
      // 'adminResource',
      // 'systemConfig',
      // 'backUpData',
      // 'systemOptionLog',
      // 'announce',
      // 'systemNotify',
      // 'ads',
      // 'contentTemp',
      // 'templateConfig',
      // 'versionManage',
      // 'content',
      // 'contentTags',
      // 'contentCategory',
      // 'contentMessage',
      // 'regUser',
      // 'helpCenter',
      // 'renderCms',
      // 'cmsTemplate',
      // 'plugin',
      // 'uploadFile',
      // 'mailTemplate',
      // 'mailDelivery',
      // 'appManage',
      // 'dictManage',
      // 'userManage',
      'orgManage',
      // 'roleManage',
      // 'orgReview',
      // 'expertReview',
    ],
    // DEV_CONFIG_MODULES_END
    mongoose: {
      client: {
        url: `mongodb://${mongohost}/zyws-xjbt0709`,
        options: {
          useCreateIndex: true,
          useUnifiedTopology: true,
          keepAlive: 3000,
        },
      },
    },
    // mongodb相关路径
    mongodb: {
      binPath: `${mongobin}`,
      backUpPath: path.join(appInfo.baseDir, 'databak/'),
    },
    redis: {
      // client: {
      //   host: '127.0.0.1',
      //   port: 6379,
      //   password: '',
      //   db: null,
      // },
      client: {
        port: 63379,
        host: 'www.beasts.wang',
        password: 'Vancy0727wangxi',
        db: null,
      },
    },
    static: {
      prefix: '/static',
      dir: [
        path.join(appInfo.baseDir, 'app/public'),
        path.join(appInfo.baseDir, 'backstage/dist'),
      ],
      maxAge: 31536000,
    },
    logger: {
      dir: path.join(appInfo.baseDir, 'logs'),
    },
    server_path: 'http://127.0.0.1:7002',
    server_api: 'http://127.0.0.1:7002/api',
    portalOidc: {
      // 新疆兵团oidc认证系统
      oidcInfo: {
        api_host: 'http://**************:10086',
        realm_name: 'myrealm',
        client_id: 'zyjk',
        client_secret: 'unity',
        redirect_uri: 'https://xmyoapi.xixids.com/xjbtcallback',
        scope: 'openid', // keycloak的登录回调地址
        grant_type: 'authorization_code',
        applicationId: 'zyjk',
        appid: '7d37bcdb-f8b2-11ef-a7c7-00d0f8000001',
      },
    },
    iServiceHost: 'http://iservice.beasts.wang',
  };
};
